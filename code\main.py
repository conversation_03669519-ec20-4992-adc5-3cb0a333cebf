#!/usr/bin/env python3
"""
Main application demonstrating the box detection system.

This is a simple, clean example showing how all components work together
to detect boxes from a camera feed with optional visualization.
"""

import argparse
import sys
from loguru import logger

from camera.camera_realsense import RealSenseCamera
from box_top.box_top_detector import BoxTopDetector
from box_top.box_detector import BoxDetector
from box_top.visualizer import Open3DVisualizer, NoOpVisualizer


def create_camera():
    """Create and configure the camera."""
    camera = RealSenseCamera()
    return camera


def create_visualizer(enable_visualization: bool):
    """Create the appropriate visualizer based on user preference."""
    if enable_visualization:
        return Open3DVisualizer()
    else:
        return NoOpVisualizer()


def run_box_detection(enable_visualization: bool = True):
    """
    Run the complete box detection pipeline.

    Args:
        enable_visualization: Whether to show the 3D visualization
    """
    logger.info("🚀 Starting Box Detection System")

    # Create components
    logger.info("📹 Initializing camera...")
    camera = create_camera()

    logger.info("👁️  Setting up visualizer...")
    visualizer = create_visualizer(enable_visualization)

    logger.info("🔍 Creating box top detector...")
    box_top_detector = BoxTopDetector(camera, visualizer=visualizer)

    logger.info("🎯 Creating box detector...")
    box_detector = BoxDetector(box_top_detector)

    # Run detection
    detected_count = 0

    try:
        logger.info("✨ Starting detection pipeline...")
        logger.info("Press Ctrl+C to stop, or ESC in visualization window")

        for detected_box in box_detector.detect_boxes():
            if detected_box is not None:
                detected_count += 1

                logger.info(
                    f"📦 Box #{detected_count} detected!"
                    f"   Position: [{detected_box.center[0] * 1000:3.0f}, {detected_box.center[1] * 1000:3.0f}]"
                    f"   Angle: {detected_box.rotation[0] % 180:3.0f}°"
                    f"   Size: {detected_box.extent[0] * 1000:3.0f} x {detected_box.extent[1] * 1000:3.0f} x {(detected_box.center[2] + detected_box.extent[2]) * 1000:3.0f} mm"
                )

                # Optional: Add processing logic here
                # For example: send to robot, log to database, etc.

    except KeyboardInterrupt:
        logger.info("⏹️  Detection stopped by user")
    except Exception as e:
        logger.error(f"❌ Error during detection: {e}")
        raise
    finally:
        logger.info(f"✅ Detection completed. Total boxes detected: {detected_count}")


def run_mock_detection():
    """
    Run box detection with mock data for testing/demo purposes.
    """
    logger.info("🎭 Starting Mock Box Detection System")

    from box_top.box_top_detector_mock import BoxTopDetectorMock

    # Create mock components
    logger.info("📁 Loading mock data...")
    mock_detector = BoxTopDetectorMock("../data/box_top_recording_01.txt", loop=False)

    logger.info("🎯 Creating box detector...")
    box_detector = BoxDetector(mock_detector)

    # Run detection
    detected_count = 0

    try:
        logger.info("✨ Starting mock detection...")

        for detected_box in box_detector.detect_boxes():
            if detected_box is not None:
                detected_count += 1

                logger.info(f"📦 Box #{detected_count} detected!")
                logger.info(f"   Position: {detected_box.center}")
                logger.info(f"   Angle: {detected_box.rotation[0] % 90:.0f}°")
                logger.info(
                    f"   Top size: {detected_box.extent[0] * 1000:.0f} x {detected_box.extent[1] * 1000:.0f} mm"
                )

    except KeyboardInterrupt:
        logger.info("⏹️  Mock detection stopped by user")
    except Exception as e:
        logger.error(f"❌ Error during mock detection: {e}")
        raise
    finally:
        logger.info(f"✅ Mock detection completed. Total boxes detected: {detected_count}")


def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(
        description="Box Detection System - Detect boxes from camera or mock data",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python main.py                    # Run with camera and visualization
  python main.py --no-viz           # Run with camera, no visualization
  python main.py --mock             # Run with mock data
        """,
    )

    parser.add_argument("--mock", action="store_true", help="Use mock data instead of real camera")

    parser.add_argument(
        "--no-viz", action="store_true", help="Disable 3D visualization (headless mode)"
    )

    args = parser.parse_args()

    try:
        if args.mock:
            run_mock_detection()
        else:
            run_box_detection(enable_visualization=not args.no_viz)

    except KeyboardInterrupt:
        logger.info("👋 Goodbye!")
        sys.exit(0)
    except Exception as e:
        logger.error(f"💥 Fatal error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
