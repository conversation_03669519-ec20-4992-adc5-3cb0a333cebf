[pytest]
markers =
    hardware: marks tests as requiring hardware (deselect with '-m "not hardware"')
    slow: marks tests as slow (deselect with '-m "not slow"')
    integration: marks tests as integration tests
    unit: marks tests as unit tests

testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*

# Minimum version
minversion = 6.0

# Add current directory to Python path
addopts = --tb=short -v

# Ignore warnings from dependencies
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
    ignore::UserWarning:open3d.*
    ignore::UserWarning:pyrealsense2.*
