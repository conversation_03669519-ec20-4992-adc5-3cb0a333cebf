{"configurations": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "includePath": ["${workspaceFolder}/**", "C:/Users/<USER>/AppData/Local/Arduino15/packages/arduino/hardware/avr/1.8.6/cores/arduino", "C:/Users/<USER>/AppData/Local/Arduino15/packages/arduino/hardware/avr/1.8.6//libraries/**", "C:/Users/<USER>/AppData/Local/Arduino15/packages/arduino/tools/avr-gcc/7.3.0-atmel3.6.1-arduino7/include/**", "C:/Users/<USER>/AppData/Local/Arduino15/packages/arduino/hardware/avr/1.8.6/variants/standard", "C:/Users/<USER>/Documents/Arduino/libraries/**"], "defines": ["ARDUINO=10819", "ARDUINO_AVR_UNO", "ARDUINO_ARCH_AVR", "__AVR_ATmega328P__", "F_CPU=16000000L"], "compilerPath": "C:/Users/<USER>/AppData/Local/Arduino15/packages/arduino/tools/avr-gcc/7.3.0-atmel3.6.1-arduino7/bin/avr-gcc.exe", "cStandard": "c11", "cppStandard": "c++11", "intelliSenseMode": "gcc-x64"}], "version": 4}