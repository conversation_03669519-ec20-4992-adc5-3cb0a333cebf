from packer_help_functions import create_random_boxes, create_random_from_set
from pallet import <PERSON><PERSON><PERSON>
from packer_bullet import BulletPack<PERSON>
from box import Box
from visualise_o3d import visualise_o3d, visualise_o3d_steps


# 150 random boxes
random_boxes = create_random_boxes(80, (0.1, 0.6), (0.1, 0.4), (0.1, 0.4), int=False)

# 15 random boxes but 2 don't fit on the same height
random_large_boxes = create_random_boxes(15, (0.6, 0.7), (0.6, 0.7), (0.06, 0.25), int=False)

# 100 0.28x0.18x0.2 boxes
uniform_boxes = [Box(0.28, 0.18, 0.2) for _ in range(100)]

# 15 big boxes followed by 45 small boxes
big_to_small = [Box(0.6, 0.6, 0.2) for _ in range(15)] + [Box(0.3, 0.2, 0.2) for _ in range(45)]

# 200 boxes from 6 possible sizes
random_from_set = create_random_from_set(200)


if __name__ == "__main__":
    # FILL IN QUE YOU WOULD LIKE TO TEST
    box_queue = random_large_boxes

    buffer_size = 4
    pallet = Pallet(1.22, 1.27, 0.1, 1.1)

    packerb = BulletPacker(pallet)
    packerb.pack_boxes(
        packerb.pallet,
        queue=box_queue,
        buffer_size=buffer_size,
        disable_prints=False,
        max_skewdness=10,
        create_video=True,
    )

    # CHOOSE WHAT VISUALISER YOU WANT
    # visualise_o3d_steps(packerb) #one box at a time
    visualise_o3d(packerb)  # final boxes
